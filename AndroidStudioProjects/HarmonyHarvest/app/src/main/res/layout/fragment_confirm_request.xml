<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".fragment.ConfirmRequestFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/backgroundColour">

        <androidx.cardview.widget.CardView
            android:layout_width="350dp"
            android:layout_height="80dp"
            android:layout_margin="20dp"
            android:elevation="10dp"
            app:cardCornerRadius="8dp"

            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/needBefore">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="10dp"
                android:background="@color/white">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Description: "
                    android:textColor="@color/black"
                    android:textSize="15dp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.cardview.widget.CardView>

        <TextView
            android:id="@+id/profile"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:text="Confirm Request"
            android:textColor="@color/black"
            android:textSize="20dp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <com.mikhaellopez.circularimageview.CircularImageView
            android:id="@+id/profileImageView"
            android:layout_width="130dp"
            android:layout_height="130dp"
            android:layout_marginTop="40dp"
            android:background="@drawable/ic_circle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/profile"/>

        <TextView
            android:id="@+id/profileName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="55dp"
            android:text="User Name"
            android:textColor="@color/black"
            android:textSize="23dp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/profileImageView" />

        <TextView
            android:id="@+id/tvQuantity"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="30dp"
            android:text="Quantity:"
            android:textColor="@color/black"
            android:textSize="17dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/profileName" />

        <TextView
            android:id="@+id/tvLocation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="20dp"
            android:text="Location:"
            android:textColor="@color/black"
            android:textSize="17dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvQuantity" />

        <TextView
            android:id="@+id/needBefore"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="20dp"
            android:text="Need Before:"
            android:textColor="@color/black"
            android:textSize="17dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvLocation" />

        <Button
            android:id="@+id/confirmBt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="45dp"
            android:background="@drawable/cvector"
            android:text="Confirm"
            android:textColor="@color/black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />




    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
