<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".fragment.RequestListFragment">
        <TextView
            android:id="@+id/rq1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Live Request"
            android:textSize="16sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_margin="15dp"
            />
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvLiveRequest"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/rq1"
            android:layout_marginTop="10dp"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
