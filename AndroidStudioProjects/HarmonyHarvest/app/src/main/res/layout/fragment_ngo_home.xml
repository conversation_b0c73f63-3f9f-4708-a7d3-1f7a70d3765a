<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".fragment.NgoHomeFragment">

        <TextView
            android:id="@+id/tvWelcomeNgo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="Welcome Back"
            android:textColor="@color/black"
            android:textSize="24dp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvWelcomeNgoName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Name"
            android:textColor="@color/black"
            android:textSize="24dp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvWelcomeNgo" />

        <androidx.cardview.widget.CardView
            android:id="@+id/ivNgoCd"
            android:layout_width="0dp"
            android:layout_height="300dp"
            android:layout_margin="20dp"
            android:elevation="10dp"
            app:cardCornerRadius="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvWelcomeNgoName">

            <ImageView
                android:id="@+id/ivNgo"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:scaleType="fitXY"
                android:src="@drawable/intro1vector" />
        </androidx.cardview.widget.CardView>

        <Button
            android:id="@+id/btReqDonation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:background="@drawable/cvector"
            android:text="Donation Request"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivNgoCd" />

        <Button
            android:id="@+id/btReqHistory"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:background="@drawable/cvector"
            android:text="Request History"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btReqDonation" />

        <androidx.cardview.widget.CardView
            android:id="@+id/profileCard"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_margin="22dp"
            android:elevation="10dp"
            app:cardCornerRadius="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <ImageView
                android:id="@+id/ivProfile"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/teal_200"
                android:scaleType="fitXY"
                android:src="@drawable/ic_profile_icon"
                android:padding="5dp"/>
        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
