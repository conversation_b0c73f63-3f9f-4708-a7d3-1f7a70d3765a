<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".fragment.ChooseImageFragment">

        <TextView
            android:id="@+id/tvChooseImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="64dp"
            android:text="Choose Profile Image"
            android:textColor="@color/black"
            android:textSize="24sp"
            android:textStyle="bold"

            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.mikhaellopez.circularimageview.CircularImageView
            android:id="@+id/ivImage"
            android:layout_width="233dp"
            android:layout_height="233dp"
            android:layout_marginTop="18dp"
            android:background="@drawable/ic_circle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvChooseImage" />

        <Button
            android:id="@+id/submitBt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/cvector"
            android:text="Done"
            android:textColor="@color/black"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivImage" />

        <ProgressBar
            android:id="@+id/progressBar"
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivImage" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
