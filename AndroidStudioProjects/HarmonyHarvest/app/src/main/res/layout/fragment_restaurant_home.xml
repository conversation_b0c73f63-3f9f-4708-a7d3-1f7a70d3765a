<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".fragment.NgoHomeFragment">

        <TextView
            android:id="@+id/tvWelcomeRestaurant"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:text="Welcome Back"
            android:textColor="@color/black"
            android:textSize="24dp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tvWelcomeRestaurantName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Name"
            android:textColor="@color/black"
            android:textSize="24dp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvWelcomeRestaurant" />

        <androidx.cardview.widget.CardView
            android:id="@+id/ivResCd"
            android:layout_width="0dp"
            android:layout_height="250dp"
            android:layout_margin="20dp"
            android:elevation="10dp"
            app:cardCornerRadius="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvWelcomeRestaurantName">

            <ImageView
                android:id="@+id/ivRes"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:scaleType="fitXY"
                android:src="@drawable/intro1vector" />
        </androidx.cardview.widget.CardView>

        <Button
            android:id="@+id/btMakeDonation"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:background="@drawable/cvector"
            android:text="Make a donation"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivResCd" />

        <Button
            android:id="@+id/btDonations"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:background="@drawable/cvector"
            android:text="Your Donations"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btMakeDonation" />

        <Button
            android:id="@+id/btDonationReq"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:background="@drawable/cvector"
            android:text="Donation Requests"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btDonations" />

        <androidx.cardview.widget.CardView
            android:id="@+id/profileCard"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_margin="22dp"
            android:elevation="10dp"
            app:cardCornerRadius="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent">

            <ImageView
                android:id="@+id/ivProfile"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/teal_200"
                android:scaleType="fitXY"
                android:src="@drawable/ic_profile_icon"
                android:padding="5dp"/>
        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
