<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".fragment.UserProfileFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/backgroundColour">

        <TextView
            android:id="@+id/profile"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="55dp"
            android:text="Profile"
            android:textColor="@color/black"
            android:textSize="20dp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.mikhaellopez.circularimageview.CircularImageView
            android:id="@+id/profileImageView"
            android:layout_width="150dp"
            android:layout_height="150dp"
            android:layout_marginTop="20dp"
            android:scaleType="centerCrop"
            android:background="@drawable/ic_circle"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/profile"/>

        <TextView
            android:id="@+id/profileName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50dp"
            android:text="User Name"
            android:textColor="@color/black"
            android:textSize="26dp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/profileImageView" />

        <TextView
            android:id="@+id/userTypeText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="18dp"
            android:text="NGO / Restaurant"
            android:textColor="@color/app_gray"
            android:textSize="17dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/profileName" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="360dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="60dp"
            android:orientation="vertical"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/userTypeText">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/openEditPage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/teal_200">

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:background="@color/white"
                    android:src="@android:drawable/ic_menu_edit" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text="Edit Profile"
                    android:textColor="@color/black"
                    android:textSize="20dp"
                    android:textStyle="bold" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/openAboutPage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:background="@color/teal_200">

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:background="@color/white"
                    android:src="@android:drawable/star_off" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text="About"
                    android:textColor="@color/black"
                    android:textSize="20dp"
                    android:textStyle="bold" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/LogOut"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:background="@color/teal_200">

                <ImageView
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:background="@color/white"
                    android:src="@android:drawable/ic_menu_revert" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text="Log Out"
                    android:textColor="@color/black"
                    android:textSize="20dp"
                    android:textStyle="bold" />

            </androidx.appcompat.widget.LinearLayoutCompat>

        </androidx.appcompat.widget.LinearLayoutCompat>

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>
