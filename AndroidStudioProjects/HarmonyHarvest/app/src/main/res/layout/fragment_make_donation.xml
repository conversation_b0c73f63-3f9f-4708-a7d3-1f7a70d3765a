<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".fragment.MakeDonationFragment"
        android:background="@color/backgroundColour">

        <TextView
            android:id="@+id/tvFoodDetails"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Food Details:"
            android:textColor="@color/black"
            android:layout_marginTop="8dp"
            android:textSize="24dp"
            android:textStyle="bold"
            android:layout_marginStart="16dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <androidx.cardview.widget.CardView
            android:id="@+id/ivFoodImgCd"
            android:layout_width="0dp"
            android:layout_height="200dp"
            android:layout_marginVertical="16dp"
            android:layout_marginHorizontal="64dp"
            android:elevation="10dp"
            app:cardCornerRadius="16dp"
            app:layout_constraintTop_toBottomOf="@+id/tvFoodDetails"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <ImageView
                android:id="@+id/ivFoodIv"
                android:src="@drawable/intro1vector"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/white"
                android:scaleType="fitXY"/>
        </androidx.cardview.widget.CardView>
        <EditText
            android:id="@+id/etItemName"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/rounded_corner"
            android:hint="Item Name"
            android:paddingHorizontal="16dp"
            android:paddingVertical="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivFoodImgCd" />
        <EditText
            android:id="@+id/etTimeOfPrep"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/rounded_corner"
            android:hint="Time of Preparation"
            android:inputType="phone"
            android:paddingHorizontal="16dp"
            android:paddingVertical="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/etItemName" />

        <EditText
            android:id="@+id/etQuantity"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/rounded_corner"
            android:hint="Quantity"
            android:inputType="phone"
            android:paddingHorizontal="16dp"
            android:paddingVertical="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/etTimeOfPrep" />
        <EditText
            android:id="@+id/etAddress"
            android:layout_width="300dp"
            android:layout_height="100dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/rounded_corner"
            android:hint="Address"
            android:paddingHorizontal="16dp"
            android:paddingVertical="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/etQuantity" />

        <TextView
            android:layout_marginTop="8dp"
            android:text="Utensils used?"
            app:layout_constraintStart_toStartOf="@+id/etAddress"
            android:id="@+id/tvUtensils"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toBottomOf="@+id/etAddress"/>
        <RadioGroup xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/rgUtensils"
            android:gravity="left"
            app:layout_constraintTop_toBottomOf="@id/tvUtensils"
            app:layout_constraintStart_toStartOf="@id/tvUtensils"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">
            <RadioButton android:id="@+id/radio_Yes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Yes"/>
            <RadioButton android:id="@+id/radio_No"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="No"/>
        </RadioGroup>
        <Button
            android:layout_marginTop="32dp"
            android:id="@+id/btSubmit"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/cvector"
            android:text="Submit"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/rgUtensils" />

        <ProgressBar
            android:id="@+id/progressBar2"
            style="?android:attr/progressBarStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@id/rgUtensils"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
