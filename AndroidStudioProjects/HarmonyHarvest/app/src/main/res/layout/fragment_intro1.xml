<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/backgroundColour"
        tools:context=".fragment.Intro1Fragment">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/intro1img"
            android:layout_width="360dp"
            android:layout_height="240dp"
            android:src="@drawable/ic_intro1vector"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="160dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="8dp"
            android:layout_marginTop="8dp"
            android:gravity="center"
            android:text="Khana Waste Mat Karoooo"
            android:textColor="@color/black"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/intro1img" />

        <Button
            android:id="@+id/intro1bt"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginBottom="32dp"
            android:background="@drawable/ic_ellipse"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"></Button>

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_arrow"
            app:layout_constraintBottom_toBottomOf="@id/intro1bt"
            app:layout_constraintEnd_toEndOf="@id/intro1bt"
            app:layout_constraintStart_toStartOf="@id/intro1bt"
            app:layout_constraintTop_toTopOf="@id/intro1bt" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
