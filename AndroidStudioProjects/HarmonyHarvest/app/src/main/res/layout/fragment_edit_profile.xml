<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".fragment.EditProfileFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/backgroundColour">

        <TextView
            android:id="@+id/tvNewCredentials"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="30dp"
            android:layout_marginTop="185dp"
            android:text="New Credentials:"
            android:textColor="@color/black"
            android:textSize="24dp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/etName"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:background="@drawable/rounded_corner"
            android:hint="Name"
            android:paddingHorizontal="16dp"
            android:paddingVertical="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tvNewCredentials" />

        <EditText
            android:id="@+id/etPhNumber"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:background="@drawable/rounded_corner"
            android:hint="Phone Number"
            android:inputType="phone"
            android:paddingHorizontal="16dp"
            android:paddingVertical="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/etName" />

        <Button
            android:id="@+id/submitBt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="88dp"
            android:background="@drawable/cvector"
            android:text="Submit"
            android:textColor="@color/black"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/etPhNumber" />



    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
