package com.example.harmonyharvest.fragment

import android.os.Bundle
import android.provider.Settings.Global.putString
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import com.example.harmonyharvest.R

private const val ARG_PARA1="para1"
private const val ARG_PARA2="para2"

class AboutFragment: Fragment() {
    private var para1: String? = null
    private var para2: String? = null

    override
    fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            para1 = it.getString(ARG_PARA1)
            para2 = it.getString(ARG_PARA2)
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_about, container, false)
    }

    companion object {
        @JvmStatic
        fun newInstance(para1: String, para2: String) =
            AboutFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_PARA1, para1)
                    putString(ARG_PARA2, para2)
                }
            }
    }
}